import { BaseRecordMapper, IR<PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { TenantMembershipRecord } from "@features/infra/databases/records";
import { MembershipChangeRecordMapper } from "./membership-change.record-mapper";
import { MembershipCycleRecordMapper } from "@features/infra/databases/record-mappers/membership-cyclle.record-mapper";
import { TenantMembershipExternalMappingRecordMapper } from "@features/infra/databases/record-mappers/tenant-membership-external.record-mapper";
import {
  MembershipCycleDto,
  TenantMembershipDto,
  MembershipChangeDto,
  TenantMembershipExternalMappingDto,
} from "@features/domain";

export type ITenantMembershipRecordMapper = IRecordMapper<
  TenantMembershipDto,
  TenantMembershipRecord
>;

export class TenantMembershipRecordMapper
  extends BaseRecordMapper
  implements ITenantMembershipRecordMapper
{
  private membershipChangeRecordMapper = new MembershipChangeRecordMapper();
  private membershipCycleRecordMapper = new MembershipCycleRecordMapper();
  private membershipExternalMappingRecordMapper =
    new TenantMembershipExternalMappingRecordMapper();

  fromRecordToDto(
    record: Partial<TenantMembershipRecord>
  ): Partial<TenantMembershipDto> {
    const membershipChanges = record.membership_changes
      ? this.membershipChangeRecordMapper.fromRecordsToDtos(
          record.membership_changes
        )
      : undefined;
    const membershipCycle = record.membership_cycle
      ? this.membershipCycleRecordMapper.fromRecordToDto(
          record.membership_cycle
        )
      : undefined;

    const externalMappings = record.external_mappings
      ? record.external_mappings.map((mappingRecord) =>
          this.membershipExternalMappingRecordMapper.fromRecordToDto(
            mappingRecord
          )
        )
      : undefined;

    return {
      id: record.id,
      tenantId: record.tenant_id,
      membershipCycleId: record.membership_cycle_id,
      isActive: record.is_active,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
      membershipCycle: membershipCycle as MembershipCycleDto,
      membershipChanges: membershipChanges as MembershipChangeDto[],
      externalTenantMembershipMappings:
        externalMappings as TenantMembershipExternalMappingDto[],
    };
  }

  fromDtoToRecord(
    dto: Partial<TenantMembershipDto>
  ): Partial<TenantMembershipRecord> {
    return {
      id: dto.id,
      tenant_id: dto.tenantId,
      membership_cycle_id: dto.membershipCycleId,
      is_active: dto.isActive,
      created_at: dto.createdAt,
      created_by: dto.createdBy,
      updated_at: dto.updatedAt,
      updated_by: dto.updatedBy,
    };
  }
}
