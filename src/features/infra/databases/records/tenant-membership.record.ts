import { Nullable } from "@heronjs/common";
import { MembershipChangeRecord } from "./membership-change.record";
import { MembershipCycleRecord } from "@features/infra/databases/records/membership-cycle.record";
import { TenantMembershipExternalMappingRecord } from "@features/infra/databases/records/tenant-membership-external-mapping.record";

export type TenantMembershipRecord = {
  id: string;
  tenant_id: string;
  membership_cycle_id: string;
  is_active: boolean;
  created_at: Date;
  created_by: string;
  updated_at: Nullable<Date>;
  updated_by: Nullable<string>;
  membership_changes?: MembershipChangeRecord[];
  membership_cycle?: MembershipCycleRecord;
  external_mappings?: TenantMembershipExternalMappingRecord[];
};
