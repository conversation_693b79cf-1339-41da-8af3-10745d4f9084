const SCHEMA = "membership";
const TABLE_NAME = "tbl_tenant_membership";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("tenant_id").notNullable();
    table.uuid("membership_cycle_id").notNullable();
    table.boolean("is_active").notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.uuid("created_by").notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();
    table.uuid("updated_by").nullable();

    // Foreign keys
    table
      .foreign("membership_cycle_id")
      .references("id")
      .inTable(`${SCHEMA}.tbl_membership_cycles`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
