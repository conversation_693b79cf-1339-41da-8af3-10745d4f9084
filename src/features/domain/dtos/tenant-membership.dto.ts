import { Nullable } from "@heronjs/common";
import { MembershipChangeDto } from "./membership-change.dto";
import { MembershipCycleDto } from "@features/domain/dtos/membership-cycle.dto";
import { TenantMembershipExternalMappingDto } from "@features/domain/dtos/tenant-membership-external-mapping.dto";

export type TenantMembershipDto = {
  id: string;
  tenantId: string;
  membershipCycleId: string;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  membershipChanges: MembershipChangeDto[];
  membershipCycle?: MembershipCycleDto;
  externalTenantMembershipMappings?: TenantMembershipExternalMappingDto[];
};
