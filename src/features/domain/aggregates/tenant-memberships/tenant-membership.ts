import {
  AggregateRoot,
  IAggregateRoot,
  AggregateRootConstructorPayload,
} from "@cbidigital/aqua-ddd";
import {
  CreateTenantMembershipInput,
  UpdateTenantMembershipInput,
} from "./types";
import {
  MembershipChange,
  IMembershipChange,
  CreateMembershipChangeInput,
  ITenantMembershipExternalMapping,
} from "./entities";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";
import { NoActiveMembershipChangeError } from "@features/domain/aggregates/tenant-memberships/errors";

export type TenantMembershipProps = {
  tenantId: string;
  membershipCycleId: string;
  isActive: boolean;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  membershipChanges: IMembershipChange[];
  externalTenantMembershipMappings?: ITenantMembershipExternalMapping[];
};

export type TenantMembershipMethods = {
  create(payload: CreateTenantMembershipInput): Promise<void>;
  update(payload: UpdateTenantMembershipInput): Promise<void>;
  cancel(): Promise<void>;
  expire(): Promise<void>;
  activate(): Promise<void>;
  deactivate(): Promise<void>;
  addMembershipChange(
    payload: Omit<CreateMembershipChangeInput, "tenantMembershipId">
  ): Promise<IMembershipChange>;
  getMembershipChanges(): IMembershipChange[];
  getCurrentMembershipChange(): Nullable<IMembershipChange>;
  extendTrialPeriod(days: number): Promise<void>;
  onPaymentSucceeded(): Promise<void>;
};

export type ITenantMembership = IAggregateRoot<
  TenantMembershipProps,
  TenantMembershipMethods
>;

export class TenantMembership
  extends AggregateRoot<TenantMembershipProps, TenantMembershipMethods>
  implements ITenantMembership
{
  static AGGREGATE_NAME = "tenant-membership";

  constructor(payload: AggregateRootConstructorPayload<TenantMembershipProps>) {
    super(payload);
  }

  /** Props **/
  get tenantId(): string {
    return this.props.tenantId;
  }

  get membershipCycleId(): string {
    return this.props.membershipCycleId;
  }

  get isActive(): boolean {
    return this.props.isActive;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  get membershipChanges(): IMembershipChange[] {
    return this.props.membershipChanges;
  }

  /** Methods **/
  private setTenantId(payload?: string) {
    if (payload !== undefined) this.setProp("tenantId", payload);
  }

  private setMembershipCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipCycleId", payload);
  }

  private setIsActive(payload?: boolean) {
    if (payload !== undefined) this.setProp("isActive", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  async create(payload: CreateTenantMembershipInput): Promise<void> {
    this.setId(randomUUID());
    this.setTenantId(payload.tenantId);
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setIsActive(true);
    this.setCreatedAt(new Date());
    this.setCreatedBy(payload.createdBy);
    this.setProp("membershipChanges", []);
  }

  async update(payload: UpdateTenantMembershipInput): Promise<void> {
    this.setId(payload.id);
    this.setTenantId(payload.tenantId);
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setIsActive(payload.isActive);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(payload.updatedBy);
  }

  async cancel(): Promise<void> {
    this.setIsActive(false);
    this.setUpdatedAt(new Date());
  }

  async expire(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async activate(): Promise<void> {
    this.setIsActive(true);
    this.setUpdatedAt(new Date());
  }

  async deactivate(): Promise<void> {
    this.setIsActive(false);
    this.setUpdatedAt(new Date());
  }

  async onPaymentSucceeded(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  /**
   * Adds a membership change with the provided details
   *
   * @param payload - The membership change input data
   * @returns The created membership change entity
   */
  async addMembershipChange(
    payload: Omit<CreateMembershipChangeInput, "tenantMembershipId">
  ): Promise<IMembershipChange> {
    const membershipChange = new MembershipChange();
    await membershipChange.create({ ...payload, tenantMembershipId: this.id });

    const membershipChanges = [...this.membershipChanges, membershipChange];
    this.setProp("membershipChanges", membershipChanges);

    return membershipChange;
  }

  getMembershipChanges(): IMembershipChange[] {
    return this.membershipChanges;
  }

  getCurrentMembershipChange(): Nullable<IMembershipChange> {
    if (!this.membershipChanges.length) return null;

    const now = Date.now();
    const currentMembershipChange = this.membershipChanges.find(
      (change) => change.startAt <= now && !change.endAt
    );

    return currentMembershipChange ?? null;
  }

  async extendTrialPeriod(days: number): Promise<void> {
    const DAY_IN_MS = 24 * 60 * 60 * 1000;
    const currentMembershipChange = this.getCurrentMembershipChange();
    if (!currentMembershipChange) throw new NoActiveMembershipChangeError();
    const now = Date.now();
    await currentMembershipChange.end(now);
    await this.addMembershipChange({
      startAt: now,
      expireAt: now + days * DAY_IN_MS,
    });
  }
}
