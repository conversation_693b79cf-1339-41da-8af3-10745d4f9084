import StatusCodes from "http-status-codes";
import { APIError, HttpResponseUtils, Logger } from "@heronjs/common";
import {
  Next,
  HttpRequest,
  HttpResponse,
  ExpressErrorInterceptor,
} from "@heronjs/express";
import {
  MembershipNotFoundError,
  SameMembershipCycleError,
  DuplicateBillingCycleError,
  ActiveMembershipExistsError,
  MissingTrialPeriodDaysError,
  MembershipCycleNotFoundError,
  TenantMembershipNotFoundError,
  MembershipExternalNotFoundError,
  CannotChangeCustomToNonCustomError,
  MissingTenantIdForCustomMembershipError,
} from "@features/domain";

const logger = new Logger("GlobalApiErrorInterceptor");
export const GlobalErrorInterceptor: ExpressErrorInterceptor = (
  originalError: any,
  req: HttpRequest,
  res: HttpResponse,
  next: Next
) => {
  if (!originalError) return next();

  let err = originalError;

  switch (true) {
    case err.constructor.name === "DatabaseError": {
      err = transformDatabaseError(err);
      break;
    }
  }

  const httpStatusCode = getHttpStatusCode(err);
  if (httpStatusCode === StatusCodes.INTERNAL_SERVER_ERROR) {
    err = new APIError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      "Internal server error."
    );
  }

  return res.status(httpStatusCode).send(HttpResponseUtils.error(err));
};

const transformDatabaseError = (err: any) => {
  return err;
};

const getHttpStatusCode = (err: any) => {
  let httpStatusCode = StatusCodes.INTERNAL_SERVER_ERROR;
  switch (true) {
    case err instanceof MembershipNotFoundError:
    case err instanceof DuplicateBillingCycleError:
    case err instanceof MembershipCycleNotFoundError:
    case err instanceof MissingTenantIdForCustomMembershipError:
    case err instanceof SameMembershipCycleError:
    case err instanceof TenantMembershipNotFoundError:
    case err instanceof MembershipExternalNotFoundError:
    case err instanceof CannotChangeCustomToNonCustomError:
    case err instanceof MissingTrialPeriodDaysError:
    case err instanceof ActiveMembershipExistsError:
      httpStatusCode = StatusCodes.BAD_REQUEST;
      break;

    default:
      httpStatusCode = StatusCodes.INTERNAL_SERVER_ERROR;
      logger.error(err.message, err);
  }

  return httpStatusCode;
};
